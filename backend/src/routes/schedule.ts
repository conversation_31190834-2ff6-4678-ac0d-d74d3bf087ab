import express from 'express';
import { initializeDatabase } from '../database/connection.js';
import { logger } from '../utils/logger';

const router = express.Router();

// GET /api/schedule/:userId - Get scheduled outfits for a user
router.get('/:userId', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  const { userId } = req.params;
  const { startDate, endDate } = req.query;
  
  try {
    const db = await initializeDatabase();

    let query = `
      SELECT 
        so.*,
        o.name as outfit_name,
        o.description as outfit_description,
        o.image_url as outfit_image_url
      FROM scheduled_outfits so
      LEFT JOIN outfits o ON so.outfit_id = o.id
      WHERE so.user_id = $1
    `;
    
    const params = [userId];
    
    if (startDate && endDate) {
      query += ` AND so.scheduled_date BETWEEN $2 AND $3`;
      params.push(startDate as string, endDate as string);
    }
    
    query += ` ORDER BY so.scheduled_date ASC`;

    const result = await db.query(query, params);

    logger.info('SCHEDULE_GET', 'Retrieved scheduled outfits', {
      requestId,
      userId,
      count: result.rows.length,
      startDate,
      endDate
    });

    res.json({
      success: true,
      data: result.rows,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('SCHEDULE_GET_ERROR', 'Failed to retrieve scheduled outfits', {
      requestId,
      userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Failed to retrieve scheduled outfits',
      timestamp: new Date().toISOString()
    });
  }
});

// POST /api/schedule - Create or update a scheduled outfit
router.post('/', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    const {
      userId,
      outfitId,
      scheduledDate,
      notes,
      weatherForecast
    } = req.body;

    // Validate required fields
    if (!userId || !scheduledDate) {
      return res.status(400).json({
        error: 'Missing required fields: userId, scheduledDate',
        timestamp: new Date().toISOString()
      });
    }

    const db = await initializeDatabase();

    // Check if there's already a scheduled outfit for this date
    const existingResult = await db.query(
      'SELECT id FROM scheduled_outfits WHERE user_id = $1 AND scheduled_date = $2',
      [userId, scheduledDate]
    );

    let result;
    if (existingResult.rows.length > 0) {
      // Update existing scheduled outfit
      result = await db.query(`
        UPDATE scheduled_outfits 
        SET outfit_id = $3, notes = $4, weather_forecast = $5, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1 AND scheduled_date = $2
        RETURNING *
      `, [userId, scheduledDate, outfitId, notes, weatherForecast || {}]);
    } else {
      // Create new scheduled outfit
      result = await db.query(`
        INSERT INTO scheduled_outfits (
          user_id, outfit_id, scheduled_date, notes, weather_forecast, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
      `, [userId, outfitId, scheduledDate, notes, weatherForecast || {}]);
    }

    const scheduledOutfit = result.rows[0];

    logger.info('SCHEDULE_SAVE', 'Successfully saved scheduled outfit', {
      requestId,
      scheduledOutfitId: scheduledOutfit.id,
      userId,
      scheduledDate,
      outfitId
    });

    res.status(201).json({
      success: true,
      data: scheduledOutfit,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('SCHEDULE_SAVE_ERROR', 'Failed to save scheduled outfit', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Failed to save scheduled outfit',
      timestamp: new Date().toISOString()
    });
  }
});

// DELETE /api/schedule/:id - Delete a scheduled outfit
router.delete('/:id', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  const { id } = req.params;
  
  try {
    const db = await initializeDatabase();

    const result = await db.query(
      'DELETE FROM scheduled_outfits WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Scheduled outfit not found',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('SCHEDULE_DELETE', 'Successfully deleted scheduled outfit', {
      requestId,
      scheduledOutfitId: id
    });

    res.json({
      success: true,
      message: 'Scheduled outfit deleted successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('SCHEDULE_DELETE_ERROR', 'Failed to delete scheduled outfit', {
      requestId,
      id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Failed to delete scheduled outfit',
      timestamp: new Date().toISOString()
    });
  }
});

// PUT /api/schedule/:id/worn - Mark a scheduled outfit as worn
router.put('/:id/worn', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  const { id } = req.params;
  const { isWorn } = req.body;
  
  try {
    const db = await initializeDatabase();

    const result = await db.query(`
      UPDATE scheduled_outfits 
      SET is_worn = $2, worn_at = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id, isWorn, isWorn ? new Date() : null]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Scheduled outfit not found',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('SCHEDULE_WORN_UPDATE', 'Successfully updated worn status', {
      requestId,
      scheduledOutfitId: id,
      isWorn
    });

    res.json({
      success: true,
      data: result.rows[0],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('SCHEDULE_WORN_ERROR', 'Failed to update worn status', {
      requestId,
      id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Failed to update worn status',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
